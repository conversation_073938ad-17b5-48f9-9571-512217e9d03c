package com.talent.hunt.application.dto;

import com.talent.hunt.domain.model.QuestionType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionCreationDto {

    @NotBlank(message = "Question text is required")
    private String questionText;

    private QuestionType questionType = QuestionType.MCQ;

    @NotNull(message = "Points are required")
    @Min(value = 1, message = "Points must be at least 1")
    private Integer points = 1;

    @NotEmpty(message = "Answer options are required")
    @Valid
    private List<AnswerOptionDto> answerOptions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnswerOptionDto {
        @NotBlank(message = "Option text is required")
        private String optionText;

        private Boolean isCorrect = false;
    }
}
