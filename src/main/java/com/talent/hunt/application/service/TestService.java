package com.talent.hunt.application.service;

import com.talent.hunt.application.dto.QuestionCreationDto;
import com.talent.hunt.application.dto.TestCreationDto;
import com.talent.hunt.domain.model.*;
import com.talent.hunt.domain.repository.AnswerOptionRepository;
import com.talent.hunt.domain.repository.QuestionRepository;
import com.talent.hunt.domain.repository.TestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TestService {

    private final TestRepository testRepository;
    private final QuestionRepository questionRepository;
    private final AnswerOptionRepository answerOptionRepository;

    public Test createTest(TestCreationDto testDto, User creator) {
        log.info("Creating new test: {} by user: {}", testDto.getTitle(), creator.getUsername());
        
        Test test = Test.builder()
                .title(testDto.getTitle())
                .description(testDto.getDescription())
                .durationMinutes(testDto.getDurationMinutes())
                .passingScore(testDto.getPassingScore())
                .createdBy(creator)
                .isActive(true)
                .build();
        
        Test savedTest = testRepository.save(test);
        log.info("Test created successfully: {} with ID: {}", savedTest.getTitle(), savedTest.getId());
        return savedTest;
    }

    public Question addQuestionToTest(Long testId, QuestionCreationDto questionDto, User creator) {
        log.info("Adding question to test ID: {} by user: {}", testId, creator.getUsername());
        
        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new IllegalArgumentException("Test not found: " + testId));
        
        if (!test.getCreatedBy().getId().equals(creator.getId()) && !creator.isAdmin()) {
            throw new IllegalArgumentException("User not authorized to modify this test");
        }
        
        validateQuestionCreation(questionDto);
        
        Integer nextOrderIndex = questionRepository.findMaxOrderIndexByTest(test)
                .map(maxIndex -> maxIndex + 1)
                .orElse(0);
        
        Question question = Question.builder()
                .test(test)
                .questionText(questionDto.getQuestionText())
                .questionType(questionDto.getQuestionType())
                .points(questionDto.getPoints())
                .orderIndex(nextOrderIndex)
                .build();
        
        Question savedQuestion = questionRepository.save(question);
        
        // Add answer options
        for (int i = 0; i < questionDto.getAnswerOptions().size(); i++) {
            QuestionCreationDto.AnswerOptionDto optionDto = questionDto.getAnswerOptions().get(i);
            AnswerOption answerOption = AnswerOption.builder()
                    .question(savedQuestion)
                    .optionText(optionDto.getOptionText())
                    .isCorrect(optionDto.getIsCorrect())
                    .orderIndex(i)
                    .build();
            answerOptionRepository.save(answerOption);
        }
        
        // Update test's total questions count
        test.setTotalQuestions(test.getTotalQuestions() + 1);
        testRepository.save(test);
        
        log.info("Question added successfully to test: {}", test.getTitle());
        return savedQuestion;
    }

    @Transactional(readOnly = true)
    public List<Test> findAllActiveTests() {
        return testRepository.findByIsActiveTrueOrderByCreatedAtDesc();
    }

    @Transactional(readOnly = true)
    public List<Test> findTestsByCreator(User creator) {
        return testRepository.findByCreatedByAndIsActiveTrueOrderByCreatedAtDesc(creator);
    }

    @Transactional(readOnly = true)
    public Optional<Test> findTestById(Long testId) {
        return testRepository.findById(testId);
    }

    @Transactional(readOnly = true)
    public Optional<Test> findActiveTestWithQuestions(Long testId) {
        return testRepository.findActiveTestWithQuestions(testId);
    }

    @Transactional(readOnly = true)
    public List<Test> searchTests(String searchTerm) {
        return testRepository.findActiveTestsBySearchTerm(searchTerm);
    }

    @Transactional(readOnly = true)
    public List<Question> getTestQuestions(Long testId) {
        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new IllegalArgumentException("Test not found: " + testId));
        return questionRepository.findByTestWithAnswerOptionsOrderByOrderIndexAsc(test);
    }

    public void activateTest(Long testId, User user) {
        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new IllegalArgumentException("Test not found: " + testId));
        
        if (!test.getCreatedBy().getId().equals(user.getId()) && !user.isAdmin()) {
            throw new IllegalArgumentException("User not authorized to modify this test");
        }
        
        test.activate();
        testRepository.save(test);
        log.info("Test activated: {} by user: {}", test.getTitle(), user.getUsername());
    }

    public void deactivateTest(Long testId, User user) {
        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new IllegalArgumentException("Test not found: " + testId));
        
        if (!test.getCreatedBy().getId().equals(user.getId()) && !user.isAdmin()) {
            throw new IllegalArgumentException("User not authorized to modify this test");
        }
        
        test.deactivate();
        testRepository.save(test);
        log.info("Test deactivated: {} by user: {}", test.getTitle(), user.getUsername());
    }

    private void validateQuestionCreation(QuestionCreationDto questionDto) {
        if (questionDto.getAnswerOptions().isEmpty()) {
            throw new IllegalArgumentException("Question must have at least one answer option");
        }
        
        boolean hasCorrectAnswer = questionDto.getAnswerOptions().stream()
                .anyMatch(QuestionCreationDto.AnswerOptionDto::getIsCorrect);
        
        if (!hasCorrectAnswer) {
            throw new IllegalArgumentException("Question must have at least one correct answer");
        }
    }
}
