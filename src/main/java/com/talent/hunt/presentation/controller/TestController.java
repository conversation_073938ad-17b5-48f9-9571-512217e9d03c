package com.talent.hunt.presentation.controller;

import com.talent.hunt.application.dto.QuestionCreationDto;
import com.talent.hunt.application.dto.TestCreationDto;
import com.talent.hunt.application.service.TestService;
import com.talent.hunt.domain.model.Question;
import com.talent.hunt.domain.model.Test;
import com.talent.hunt.domain.model.User;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;

@Controller
@RequestMapping("/tests")
@RequiredArgsConstructor
@Slf4j
public class TestController {

    private final TestService testService;

    @GetMapping
    public String listTests(@AuthenticationPrincipal User currentUser, Model model) {
        List<Test> tests;
        if (currentUser.isAdmin()) {
            tests = testService.findAllActiveTests();
        } else {
            tests = testService.findTestsByCreator(currentUser);
        }
        
        model.addAttribute("tests", tests);
        model.addAttribute("currentUser", currentUser);
        return "tests/list";
    }

    @GetMapping("/create")
    public String createTestForm(Model model) {
        model.addAttribute("testCreationDto", new TestCreationDto());
        return "tests/create";
    }

    @PostMapping("/create")
    public String createTest(@Valid @ModelAttribute("testCreationDto") TestCreationDto testDto,
                            BindingResult bindingResult,
                            @AuthenticationPrincipal User currentUser,
                            Model model,
                            RedirectAttributes redirectAttributes) {
        
        if (bindingResult.hasErrors()) {
            return "tests/create";
        }
        
        try {
            Test createdTest = testService.createTest(testDto, currentUser);
            redirectAttributes.addFlashAttribute("successMessage", 
                "Test created successfully! Now add questions to your test.");
            return "redirect:/tests/" + createdTest.getId() + "/questions";
        } catch (Exception e) {
            log.error("Error creating test", e);
            model.addAttribute("errorMessage", "Failed to create test. Please try again.");
            return "tests/create";
        }
    }

    @GetMapping("/{testId}")
    public String viewTest(@PathVariable Long testId, 
                          @AuthenticationPrincipal User currentUser,
                          Model model) {
        
        Test test = testService.findTestById(testId)
                .orElseThrow(() -> new IllegalArgumentException("Test not found"));
        
        List<Question> questions = testService.getTestQuestions(testId);
        
        model.addAttribute("test", test);
        model.addAttribute("questions", questions);
        model.addAttribute("currentUser", currentUser);
        
        return "tests/view";
    }

    @GetMapping("/{testId}/questions")
    public String manageQuestions(@PathVariable Long testId,
                                 @AuthenticationPrincipal User currentUser,
                                 Model model) {
        
        Test test = testService.findTestById(testId)
                .orElseThrow(() -> new IllegalArgumentException("Test not found"));
        
        if (!test.getCreatedBy().getId().equals(currentUser.getId()) && !currentUser.isAdmin()) {
            throw new IllegalArgumentException("Access denied");
        }
        
        List<Question> questions = testService.getTestQuestions(testId);
        
        model.addAttribute("test", test);
        model.addAttribute("questions", questions);
        model.addAttribute("questionCreationDto", new QuestionCreationDto());
        
        return "tests/questions";
    }

    @PostMapping("/{testId}/questions")
    public String addQuestion(@PathVariable Long testId,
                             @Valid @ModelAttribute("questionCreationDto") QuestionCreationDto questionDto,
                             BindingResult bindingResult,
                             @AuthenticationPrincipal User currentUser,
                             Model model,
                             RedirectAttributes redirectAttributes) {
        
        if (bindingResult.hasErrors()) {
            Test test = testService.findTestById(testId)
                    .orElseThrow(() -> new IllegalArgumentException("Test not found"));
            List<Question> questions = testService.getTestQuestions(testId);
            
            model.addAttribute("test", test);
            model.addAttribute("questions", questions);
            return "tests/questions";
        }
        
        try {
            testService.addQuestionToTest(testId, questionDto, currentUser);
            redirectAttributes.addFlashAttribute("successMessage", "Question added successfully!");
            return "redirect:/tests/" + testId + "/questions";
        } catch (Exception e) {
            log.error("Error adding question", e);
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
            return "redirect:/tests/" + testId + "/questions";
        }
    }
}
