package com.talent.hunt.presentation.controller;

import com.talent.hunt.application.service.TestService;
import com.talent.hunt.application.service.TestSessionService;
import com.talent.hunt.domain.model.Test;
import com.talent.hunt.domain.model.TestSession;
import com.talent.hunt.domain.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final TestService testService;
    private final TestSessionService testSessionService;

    @GetMapping
    public String dashboard(@AuthenticationPrincipal User currentUser, Model model) {
        List<Test> availableTests = testService.findAllActiveTests();
        List<TestSession> userSessions = testSessionService.findUserSessions(currentUser);
        Optional<TestSession> activeSession = testSessionService.findActiveSessionByUser(currentUser);
        
        model.addAttribute("availableTests", availableTests);
        model.addAttribute("userSessions", userSessions);
        model.addAttribute("activeSession", activeSession.orElse(null));
        model.addAttribute("currentUser", currentUser);
        
        return "dashboard/index";
    }
}
