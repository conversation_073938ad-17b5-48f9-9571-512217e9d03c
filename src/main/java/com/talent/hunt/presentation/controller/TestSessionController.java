package com.talent.hunt.presentation.controller;

import com.talent.hunt.application.service.TestService;
import com.talent.hunt.application.service.TestSessionService;
import com.talent.hunt.domain.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;

@Controller
@RequestMapping("/sessions")
@RequiredArgsConstructor
@Slf4j
public class TestSessionController {

    private final TestSessionService testSessionService;
    private final TestService testService;

    @PostMapping("/start/{testId}")
    public String startTestSession(@PathVariable Long testId,
                                  @AuthenticationPrincipal User currentUser,
                                  RedirectAttributes redirectAttributes) {
        
        try {
            TestSession session = testSessionService.startTestSession(testId, currentUser);
            return "redirect:/sessions/" + session.getId() + "/take";
        } catch (Exception e) {
            log.error("Error starting test session", e);
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
            return "redirect:/dashboard";
        }
    }

    @GetMapping("/{sessionId}/take")
    public String takeTest(@PathVariable Long sessionId,
                          @AuthenticationPrincipal User currentUser,
                          Model model) {
        
        TestSession session = testSessionService.findSessionById(sessionId)
                .orElseThrow(() -> new IllegalArgumentException("Session not found"));
        
        if (!session.getUser().getId().equals(currentUser.getId())) {
            throw new IllegalArgumentException("Access denied");
        }
        
        if (session.getSessionStatus() != SessionStatus.IN_PROGRESS) {
            return "redirect:/sessions/" + sessionId + "/result";
        }
        
        List<Question> questions = testService.getTestQuestions(session.getTest().getId());
        List<UserResponse> userResponses = testSessionService.getSessionResponses(sessionId);
        
        model.addAttribute("session", session);
        model.addAttribute("questions", questions);
        model.addAttribute("userResponses", userResponses);
        model.addAttribute("currentUser", currentUser);
        
        return "sessions/take";
    }

    @PostMapping("/{sessionId}/answer")
    public String submitAnswer(@PathVariable Long sessionId,
                              @RequestParam Long questionId,
                              @RequestParam Long selectedOptionId,
                              @AuthenticationPrincipal User currentUser,
                              RedirectAttributes redirectAttributes) {
        
        try {
            testSessionService.submitAnswer(sessionId, questionId, selectedOptionId, currentUser);
            redirectAttributes.addFlashAttribute("successMessage", "Answer submitted successfully!");
        } catch (Exception e) {
            log.error("Error submitting answer", e);
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
        }
        
        return "redirect:/sessions/" + sessionId + "/take";
    }

    @PostMapping("/{sessionId}/complete")
    public String completeTest(@PathVariable Long sessionId,
                              @AuthenticationPrincipal User currentUser,
                              RedirectAttributes redirectAttributes) {
        
        try {
            testSessionService.completeTestSession(sessionId, currentUser);
            redirectAttributes.addFlashAttribute("successMessage", "Test completed successfully!");
            return "redirect:/sessions/" + sessionId + "/result";
        } catch (Exception e) {
            log.error("Error completing test session", e);
            redirectAttributes.addFlashAttribute("errorMessage", e.getMessage());
            return "redirect:/sessions/" + sessionId + "/take";
        }
    }

    @GetMapping("/{sessionId}/result")
    public String viewResult(@PathVariable Long sessionId,
                            @AuthenticationPrincipal User currentUser,
                            Model model) {
        
        TestSession session = testSessionService.findSessionWithResponses(sessionId)
                .orElseThrow(() -> new IllegalArgumentException("Session not found"));
        
        if (!session.getUser().getId().equals(currentUser.getId())) {
            throw new IllegalArgumentException("Access denied");
        }
        
        List<UserResponse> userResponses = testSessionService.getSessionResponses(sessionId);
        
        model.addAttribute("session", session);
        model.addAttribute("userResponses", userResponses);
        model.addAttribute("currentUser", currentUser);
        
        return "sessions/result";
    }
}
