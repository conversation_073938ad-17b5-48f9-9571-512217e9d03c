package com.talent.hunt.presentation.controller;

import com.talent.hunt.application.service.TestService;
import com.talent.hunt.application.service.TestSessionService;
import com.talent.hunt.application.service.UserService;
import com.talent.hunt.domain.model.Test;
import com.talent.hunt.domain.model.TestSession;
import com.talent.hunt.domain.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
@RequestMapping("/admin")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    private final TestService testService;
    private final TestSessionService testSessionService;
    private final UserService userService;

    @GetMapping("/dashboard")
    public String adminDashboard(@AuthenticationPrincipal User currentUser, Model model) {
        // Get all tests for admin overview
        List<Test> allTests = testService.findAllActiveTests();
        
        // Get recent test sessions for admin overview
        List<TestSession> recentSessions = testSessionService.findRecentSessions(10);
        
        // Get user statistics
        long totalUsers = userService.getTotalUserCount();
        long activeUsers = userService.getActiveUserCount();
        
        model.addAttribute("allTests", allTests);
        model.addAttribute("recentSessions", recentSessions);
        model.addAttribute("totalUsers", totalUsers);
        model.addAttribute("activeUsers", activeUsers);
        model.addAttribute("currentUser", currentUser);
        
        return "admin/dashboard";
    }
}
