package com.talent.hunt.domain.repository;

import com.talent.hunt.domain.model.Test;
import com.talent.hunt.domain.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TestRepository extends JpaRepository<Test, Long> {

    List<Test> findByIsActiveTrueOrderByCreatedAtDesc();

    List<Test> findByCreatedByAndIsActiveTrueOrderByCreatedAtDesc(User createdBy);

    @Query("SELECT t FROM Test t WHERE t.isActive = true AND " +
           "(LOWER(t.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(t.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "ORDER BY t.createdAt DESC")
    List<Test> findActiveTestsBySearchTerm(@Param("searchTerm") String searchTerm);

    @Query("SELECT t FROM Test t JOIN FETCH t.questions q WHERE t.id = :testId AND t.isActive = true")
    Optional<Test> findActiveTestWithQuestions(@Param("testId") Long testId);

    @Query("SELECT COUNT(t) FROM Test t WHERE t.createdBy = :user AND t.isActive = true")
    long countActiveTestsByUser(@Param("user") User user);
}
