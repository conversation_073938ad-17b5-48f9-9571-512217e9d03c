package com.talent.hunt.domain.repository;

import com.talent.hunt.domain.model.AnswerOption;
import com.talent.hunt.domain.model.Question;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AnswerOptionRepository extends JpaRepository<AnswerOption, Long> {

    List<AnswerOption> findByQuestionOrderByOrderIndexAsc(Question question);

    List<AnswerOption> findByQuestionAndIsCorrectTrue(Question question);

    @Query("SELECT MAX(ao.orderIndex) FROM AnswerOption ao WHERE ao.question = :question")
    Optional<Integer> findMaxOrderIndexByQuestion(@Param("question") Question question);

    long countByQuestion(Question question);

    boolean existsByQuestionAndOrderIndex(Question question, Integer orderIndex);
}
