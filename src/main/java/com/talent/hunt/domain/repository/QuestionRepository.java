package com.talent.hunt.domain.repository;

import com.talent.hunt.domain.model.Question;
import com.talent.hunt.domain.model.Test;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface QuestionRepository extends JpaRepository<Question, Long> {

    List<Question> findByTestOrderByOrderIndexAsc(Test test);

    @Query("SELECT q FROM Question q JOIN FETCH q.answerOptions WHERE q.test = :test ORDER BY q.orderIndex ASC")
    List<Question> findByTestWithAnswerOptionsOrderByOrderIndexAsc(@Param("test") Test test);

    @Query("SELECT q FROM Question q JOIN FETCH q.answerOptions WHERE q.id = :questionId")
    Optional<Question> findByIdWithAnswerOptions(@Param("questionId") Long questionId);

    @Query("SELECT MAX(q.orderIndex) FROM Question q WHERE q.test = :test")
    Optional<Integer> findMaxOrderIndexByTest(@Param("test") Test test);

    long countByTest(Test test);

    boolean existsByTestAndOrderIndex(Test test, Integer orderIndex);
}
