package com.talent.hunt.domain.repository;

import com.talent.hunt.domain.model.Question;
import com.talent.hunt.domain.model.TestSession;
import com.talent.hunt.domain.model.UserResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserResponseRepository extends JpaRepository<UserResponse, Long> {

    List<UserResponse> findByTestSessionOrderByAnsweredAtAsc(TestSession testSession);

    Optional<UserResponse> findByTestSessionAndQuestion(TestSession testSession, Question question);

    @Query("SELECT COUNT(ur) FROM UserResponse ur WHERE ur.testSession = :testSession")
    long countByTestSession(@Param("testSession") TestSession testSession);

    @Query("SELECT COUNT(ur) FROM UserResponse ur WHERE ur.testSession = :testSession AND ur.isCorrect = true")
    long countCorrectAnswersByTestSession(@Param("testSession") TestSession testSession);

    @Query("SELECT SUM(ur.pointsEarned) FROM UserResponse ur WHERE ur.testSession = :testSession")
    Optional<Integer> sumPointsEarnedByTestSession(@Param("testSession") TestSession testSession);
}
