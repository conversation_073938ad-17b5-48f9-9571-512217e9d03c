package com.talent.hunt.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "test_sessions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestSession {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "test_id", nullable = false)
    @NotNull(message = "Test is required")
    private Test test;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "session_status", nullable = false)
    @Builder.Default
    private SessionStatus sessionStatus = SessionStatus.NOT_STARTED;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Column(nullable = false)
    @Builder.Default
    private Integer score = 0;

    @Column(name = "total_points", nullable = false)
    @Builder.Default
    private Integer totalPoints = 0;

    @OneToMany(mappedBy = "testSession", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<UserResponse> userResponses = new ArrayList<>();

    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Business methods
    public void startSession() {
        if (sessionStatus != SessionStatus.NOT_STARTED) {
            throw new IllegalStateException("Session has already been started");
        }
        this.sessionStatus = SessionStatus.IN_PROGRESS;
        this.startTime = LocalDateTime.now();
        this.totalPoints = test.getTotalPoints();
        this.updatedAt = LocalDateTime.now();
    }

    public void completeSession() {
        if (sessionStatus != SessionStatus.IN_PROGRESS) {
            throw new IllegalStateException("Session is not in progress");
        }
        this.sessionStatus = SessionStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        calculateFinalScore();
        this.updatedAt = LocalDateTime.now();
    }

    public void abandonSession() {
        if (sessionStatus == SessionStatus.COMPLETED) {
            throw new IllegalStateException("Cannot abandon a completed session");
        }
        this.sessionStatus = SessionStatus.ABANDONED;
        this.endTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public boolean isExpired() {
        if (startTime == null || test.getDurationMinutes() == null) {
            return false;
        }
        LocalDateTime expiryTime = startTime.plusMinutes(test.getDurationMinutes());
        return LocalDateTime.now().isAfter(expiryTime);
    }

    public boolean isPassed() {
        return sessionStatus == SessionStatus.COMPLETED && 
               score >= test.getPassingScore();
    }

    public void addUserResponse(UserResponse userResponse) {
        userResponses.add(userResponse);
        userResponse.setTestSession(this);
        this.updatedAt = LocalDateTime.now();
    }

    private void calculateFinalScore() {
        this.score = userResponses.stream()
                .mapToInt(UserResponse::getPointsEarned)
                .sum();
    }

    public double getScorePercentage() {
        if (totalPoints == 0) return 0.0;
        return (double) score / totalPoints * 100;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
