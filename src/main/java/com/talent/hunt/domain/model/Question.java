package com.talent.hunt.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Entity
@Table(name = "questions", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"test_id", "order_index"})
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Question {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "test_id", nullable = false)
    @NotNull(message = "Test is required")
    private Test test;

    @Column(name = "question_text", nullable = false, columnDefinition = "TEXT")
    @NotBlank(message = "Question text is required")
    private String questionText;

    @Enumerated(EnumType.STRING)
    @Column(name = "question_type", nullable = false)
    @Builder.Default
    private QuestionType questionType = QuestionType.MCQ;

    @Column(nullable = false)
    @NotNull(message = "Points are required")
    @Min(value = 1, message = "Points must be at least 1")
    @Builder.Default
    private Integer points = 1;

    @Column(name = "order_index", nullable = false)
    @NotNull(message = "Order index is required")
    @Min(value = 0, message = "Order index cannot be negative")
    private Integer orderIndex;

    @OneToMany(mappedBy = "question", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<AnswerOption> answerOptions = new ArrayList<>();

    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Business methods
    public void addAnswerOption(AnswerOption answerOption) {
        answerOptions.add(answerOption);
        answerOption.setQuestion(this);
        this.updatedAt = LocalDateTime.now();
    }

    public void removeAnswerOption(AnswerOption answerOption) {
        answerOptions.remove(answerOption);
        answerOption.setQuestion(null);
        this.updatedAt = LocalDateTime.now();
    }

    public Optional<AnswerOption> getCorrectAnswer() {
        return answerOptions.stream()
                .filter(AnswerOption::getIsCorrect)
                .findFirst();
    }

    public List<AnswerOption> getCorrectAnswers() {
        return answerOptions.stream()
                .filter(AnswerOption::getIsCorrect)
                .toList();
    }

    public boolean isAnswerCorrect(Long selectedOptionId) {
        return answerOptions.stream()
                .anyMatch(option -> option.getId().equals(selectedOptionId) && option.getIsCorrect());
    }

    public boolean hasValidAnswerOptions() {
        return !answerOptions.isEmpty() && 
               answerOptions.stream().anyMatch(AnswerOption::getIsCorrect);
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
