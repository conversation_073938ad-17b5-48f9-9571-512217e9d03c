<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: layout(~{::title}, ~{::main})}">
<head>
    <title>Admin Dashboard</title>
</head>
<body>
    <main>
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-user-shield me-2"></i>
                        Admin Dashboard - Welcome, <span th:text="${currentUser.fullName}">Admin</span>!
                    </h2>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Total Users</h5>
                                    <h3 th:text="${totalUsers}">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Active Users</h5>
                                    <h3 th:text="${activeUsers}">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Total Tests</h5>
                                    <h3 th:text="${#lists.size(allTests)}">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clipboard-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Recent Sessions</h5>
                                    <h3 th:text="${#lists.size(recentSessions)}">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-history fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- All Tests Management -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>All Tests
                            </h5>
                            <a href="/tests/create" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>Create Test
                            </a>
                        </div>
                        <div class="card-body">
                            <div th:if="${#lists.isEmpty(allTests)}" class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No tests available at the moment.</p>
                                <a href="/tests/create" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Create First Test
                                </a>
                            </div>
                            
                            <div th:if="${!#lists.isEmpty(allTests)}">
                                <div th:each="test : ${allTests}" class="card mb-3">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <h6 class="card-title mb-1" th:text="${test.title}">Test Title</h6>
                                                <p class="card-text text-muted small mb-2" th:text="${test.description}">Test Description</p>
                                                <div class="d-flex gap-3">
                                                    <small class="text-muted">
                                                        <i class="fas fa-user me-1"></i>
                                                        Created by: <span th:text="${test.createdBy.fullName}">Creator</span>
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <span th:text="${test.durationMinutes}">30</span> minutes
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-question-circle me-1"></i>
                                                        <span th:text="${test.totalQuestions}">10</span> questions
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <div class="btn-group" role="group">
                                                    <a th:href="@{'/tests/' + ${test.id}}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </a>
                                                    <a th:href="@{'/tests/' + ${test.id} + '/questions'}" class="btn btn-outline-secondary btn-sm">
                                                        <i class="fas fa-edit me-1"></i>Edit
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Test Sessions -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>Recent Test Sessions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div th:if="${#lists.isEmpty(recentSessions)}" class="text-center py-3">
                                <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                <p class="text-muted small">No test sessions yet.</p>
                            </div>
                            
                            <div th:if="${!#lists.isEmpty(recentSessions)}">
                                <div th:each="session : ${recentSessions}" 
                                     class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <div>
                                        <div class="fw-bold small" th:text="${session.test.title}">Test Title</div>
                                        <div class="text-muted small">
                                            By: <span th:text="${session.user.fullName}">User</span>
                                        </div>
                                        <div class="text-muted small">
                                            <span th:text="${#temporals.format(session.createdAt, 'MMM dd, yyyy HH:mm')}">Date</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span th:if="${session.sessionStatus.name() == 'COMPLETED'}" 
                                              class="badge bg-success">
                                            <span th:text="${session.score}">85</span>/<span th:text="${session.totalPoints}">100</span>
                                        </span>
                                        <span th:if="${session.sessionStatus.name() == 'IN_PROGRESS'}" 
                                              class="badge bg-warning">In Progress</span>
                                        <span th:if="${session.sessionStatus.name() == 'ABANDONED'}" 
                                              class="badge bg-secondary">Abandoned</span>
                                        <span th:if="${session.sessionStatus.name() == 'EXPIRED'}" 
                                              class="badge bg-danger">Expired</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/tests/create" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Create New Test
                                </a>
                                <a href="/tests" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>Manage All Tests
                                </a>
                                <a href="/admin/users" class="btn btn-outline-secondary">
                                    <i class="fas fa-users me-2"></i>Manage Users
                                </a>
                                <a href="/admin/reports" class="btn btn-outline-info">
                                    <i class="fas fa-chart-bar me-2"></i>View Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
