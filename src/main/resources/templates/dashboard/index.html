<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: layout(~{::title}, ~{::main})}">
<head>
    <title>Dashboard</title>
</head>
<body>
    <main>
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Welcome, <span th:text="${currentUser.fullName}">User</span>!
                    </h2>
                </div>
            </div>

            <!-- Active Session Alert -->
            <div th:if="${activeSession != null}" class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-clock me-2"></i>Active Test Session</h5>
                        <p class="mb-2">You have an active test session for: <strong th:text="${activeSession.test.title}">Test</strong></p>
                        <a th:href="@{'/sessions/' + ${activeSession.id} + '/take'}" class="btn btn-warning">
                            <i class="fas fa-play me-2"></i>Continue Test
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Available Tests -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Available Tests
                            </h5>
                            <a href="/tests/create" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>Create Test
                            </a>
                        </div>
                        <div class="card-body">
                            <div th:if="${#lists.isEmpty(availableTests)}" class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No tests available at the moment.</p>
                                <a href="/tests/create" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Create Your First Test
                                </a>
                            </div>
                            
                            <div th:if="${!#lists.isEmpty(availableTests)}">
                                <div th:each="test : ${availableTests}" class="card mb-3">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <h6 class="card-title mb-1" th:text="${test.title}">Test Title</h6>
                                                <p class="card-text text-muted small mb-2" th:text="${test.description}">Test Description</p>
                                                <div class="d-flex gap-3">
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <span th:text="${test.durationMinutes}">30</span> minutes
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-question-circle me-1"></i>
                                                        <span th:text="${test.totalQuestions}">10</span> questions
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-trophy me-1"></i>
                                                        Passing: <span th:text="${test.passingScore}">70</span>%
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <div class="btn-group" role="group">
                                                    <a th:href="@{'/tests/' + ${test.id}}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </a>
                                                    <form th:action="@{'/sessions/start/' + ${test.id}}" method="post" class="d-inline">
                                                        <button type="submit" class="btn btn-success btn-sm" 
                                                                th:disabled="${activeSession != null}">
                                                            <i class="fas fa-play me-1"></i>Start
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Sessions -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>Recent Sessions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div th:if="${#lists.isEmpty(userSessions)}" class="text-center py-3">
                                <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                <p class="text-muted small">No test sessions yet.</p>
                            </div>
                            
                            <div th:if="${!#lists.isEmpty(userSessions)}">
                                <div th:each="session, iterStat : ${userSessions}" 
                                     th:if="${iterStat.index < 5}" 
                                     class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <div>
                                        <div class="fw-bold small" th:text="${session.test.title}">Test Title</div>
                                        <div class="text-muted small">
                                            <span th:text="${#temporals.format(session.createdAt, 'MMM dd, yyyy')}">Date</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span th:if="${session.sessionStatus.name() == 'COMPLETED'}" 
                                              class="badge bg-success">
                                            <span th:text="${session.score}">85</span>/<span th:text="${session.totalPoints}">100</span>
                                        </span>
                                        <span th:if="${session.sessionStatus.name() == 'IN_PROGRESS'}" 
                                              class="badge bg-warning">In Progress</span>
                                        <span th:if="${session.sessionStatus.name() == 'ABANDONED'}" 
                                              class="badge bg-secondary">Abandoned</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
