# Test Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# Flyway Configuration for Tests
spring.flyway.enabled=false

# Logging Configuration for Tests
logging.level.com.talent.hunt=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=DEBUG
