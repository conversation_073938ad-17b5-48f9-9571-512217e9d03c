package com.talent.hunt.application.service;

import com.talent.hunt.application.dto.QuestionCreationDto;
import com.talent.hunt.application.dto.TestCreationDto;
import com.talent.hunt.domain.model.*;
import com.talent.hunt.domain.repository.AnswerOptionRepository;
import com.talent.hunt.domain.repository.QuestionRepository;
import com.talent.hunt.domain.repository.TestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TestServiceTest {

    @Mock
    private TestRepository testRepository;

    @Mock
    private QuestionRepository questionRepository;

    @Mock
    private AnswerOptionRepository answerOptionRepository;

    @InjectMocks
    private TestService testService;

    private TestCreationDto testCreationDto;
    private QuestionCreationDto questionCreationDto;
    private User creator;
    private com.talent.hunt.domain.model.Test test;
    private Question question;

    @BeforeEach
    void setUp() {
        creator = User.builder()
                .id(1L)
                .username("creator")
                .email("<EMAIL>")
                .role(UserRole.USER)
                .isActive(true)
                .build();

        testCreationDto = TestCreationDto.builder()
                .title("Java Basics Test")
                .description("Test your Java knowledge")
                .durationMinutes(60)
                .passingScore(70)
                .build();

        test = com.talent.hunt.domain.model.Test.builder()
                .id(1L)
                .title("Java Basics Test")
                .description("Test your Java knowledge")
                .durationMinutes(60)
                .passingScore(70)
                .createdBy(creator)
                .isActive(true)
                .totalQuestions(0)
                .build();

        questionCreationDto = QuestionCreationDto.builder()
                .questionText("What is Java?")
                .questionType(QuestionType.MCQ)
                .points(5)
                .answerOptions(Arrays.asList(
                        QuestionCreationDto.AnswerOptionDto.builder()
                                .optionText("A programming language")
                                .isCorrect(true)
                                .build(),
                        QuestionCreationDto.AnswerOptionDto.builder()
                                .optionText("A coffee brand")
                                .isCorrect(false)
                                .build()
                ))
                .build();

        question = Question.builder()
                .id(1L)
                .test(test)
                .questionText("What is Java?")
                .questionType(QuestionType.MCQ)
                .points(5)
                .orderIndex(0)
                .build();
    }

    @Test
    void shouldCreateTestSuccessfully() {
        when(testRepository.save(any(com.talent.hunt.domain.model.Test.class))).thenReturn(test);

        com.talent.hunt.domain.model.Test createdTest = testService.createTest(testCreationDto, creator);

        assertThat(createdTest).isNotNull();
        assertThat(createdTest.getTitle()).isEqualTo("Java Basics Test");
        assertThat(createdTest.getDescription()).isEqualTo("Test your Java knowledge");
        assertThat(createdTest.getDurationMinutes()).isEqualTo(60);
        assertThat(createdTest.getPassingScore()).isEqualTo(70);
        assertThat(createdTest.getCreatedBy()).isEqualTo(creator);
        assertThat(createdTest.getIsActive()).isTrue();

        verify(testRepository).save(any(com.talent.hunt.domain.model.Test.class));
    }

    @Test
    void shouldAddQuestionToTestSuccessfully() {
        when(testRepository.findById(1L)).thenReturn(Optional.of(test));
        when(questionRepository.findMaxOrderIndexByTest(test)).thenReturn(Optional.empty());
        when(questionRepository.save(any(Question.class))).thenReturn(question);
        when(answerOptionRepository.save(any(AnswerOption.class))).thenReturn(new AnswerOption());
        when(testRepository.save(any(com.talent.hunt.domain.model.Test.class))).thenReturn(test);

        Question addedQuestion = testService.addQuestionToTest(1L, questionCreationDto, creator);

        assertThat(addedQuestion).isNotNull();
        verify(testRepository).findById(1L);
        verify(questionRepository).save(any(Question.class));
        verify(answerOptionRepository, times(2)).save(any(AnswerOption.class));
        verify(testRepository).save(test);
    }

    @Test
    void shouldThrowExceptionWhenTestNotFoundForAddingQuestion() {
        when(testRepository.findById(1L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> testService.addQuestionToTest(1L, questionCreationDto, creator))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Test not found: 1");

        verify(questionRepository, never()).save(any(Question.class));
    }

    @Test
    void shouldThrowExceptionWhenUserNotAuthorizedToModifyTest() {
        User unauthorizedUser = User.builder()
                .id(2L)
                .username("unauthorized")
                .role(UserRole.USER)
                .build();

        when(testRepository.findById(1L)).thenReturn(Optional.of(test));

        assertThatThrownBy(() -> testService.addQuestionToTest(1L, questionCreationDto, unauthorizedUser))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("User not authorized to modify this test");

        verify(questionRepository, never()).save(any(Question.class));
    }

    @Test
    void shouldAllowAdminToModifyAnyTest() {
        User admin = User.builder()
                .id(2L)
                .username("admin")
                .role(UserRole.ADMIN)
                .build();

        when(testRepository.findById(1L)).thenReturn(Optional.of(test));
        when(questionRepository.findMaxOrderIndexByTest(test)).thenReturn(Optional.empty());
        when(questionRepository.save(any(Question.class))).thenReturn(question);
        when(answerOptionRepository.save(any(AnswerOption.class))).thenReturn(new AnswerOption());
        when(testRepository.save(any(com.talent.hunt.domain.model.Test.class))).thenReturn(test);

        Question addedQuestion = testService.addQuestionToTest(1L, questionCreationDto, admin);

        assertThat(addedQuestion).isNotNull();
        verify(questionRepository).save(any(Question.class));
    }

    @Test
    void shouldThrowExceptionWhenQuestionHasNoAnswerOptions() {
        questionCreationDto.setAnswerOptions(List.of());

        when(testRepository.findById(1L)).thenReturn(Optional.of(test));

        assertThatThrownBy(() -> testService.addQuestionToTest(1L, questionCreationDto, creator))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Question must have at least one answer option");

        verify(questionRepository, never()).save(any(Question.class));
    }

    @Test
    void shouldThrowExceptionWhenQuestionHasNoCorrectAnswer() {
        questionCreationDto.getAnswerOptions().forEach(option -> option.setIsCorrect(false));

        when(testRepository.findById(1L)).thenReturn(Optional.of(test));

        assertThatThrownBy(() -> testService.addQuestionToTest(1L, questionCreationDto, creator))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Question must have at least one correct answer");

        verify(questionRepository, never()).save(any(Question.class));
    }

    @Test
    void shouldFindAllActiveTests() {
        List<com.talent.hunt.domain.model.Test> tests = Arrays.asList(test);
        when(testRepository.findByIsActiveTrueOrderByCreatedAtDesc()).thenReturn(tests);

        List<com.talent.hunt.domain.model.Test> foundTests = testService.findAllActiveTests();

        assertThat(foundTests).hasSize(1);
        assertThat(foundTests.get(0)).isEqualTo(test);
        verify(testRepository).findByIsActiveTrueOrderByCreatedAtDesc();
    }

    @Test
    void shouldActivateTest() {
        when(testRepository.findById(1L)).thenReturn(Optional.of(test));
        when(testRepository.save(any(com.talent.hunt.domain.model.Test.class))).thenReturn(test);

        testService.activateTest(1L, creator);

        verify(testRepository).findById(1L);
        verify(testRepository).save(test);
    }
}
