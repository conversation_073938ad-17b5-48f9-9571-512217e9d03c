package com.talent.hunt.domain.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;

import static org.assertj.core.api.Assertions.assertThat;

class TestTest {

    private com.talent.hunt.domain.model.Test test;
    private User creator;
    private Question question;

    @BeforeEach
    void setUp() {
        creator = User.builder()
                .id(1L)
                .username("creator")
                .email("<EMAIL>")
                .passwordHash("password")
                .firstName("Test")
                .lastName("Creator")
                .role(UserRole.USER)
                .isActive(true)
                .build();

        test = com.talent.hunt.domain.model.Test.builder()
                .id(1L)
                .title("Java Basics Test")
                .description("Test your Java knowledge")
                .durationMinutes(60)
                .totalQuestions(0)
                .passingScore(70)
                .isActive(true)
                .createdBy(creator)
                .questions(new ArrayList<>())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        question = Question.builder()
                .id(1L)
                .test(test)
                .questionText("What is Java?")
                .questionType(QuestionType.MCQ)
                .points(5)
                .orderIndex(0)
                .build();
    }

    @Test
    void shouldCreateTestWithValidData() {
        assertThat(test.getId()).isEqualTo(1L);
        assertThat(test.getTitle()).isEqualTo("Java Basics Test");
        assertThat(test.getDescription()).isEqualTo("Test your Java knowledge");
        assertThat(test.getDurationMinutes()).isEqualTo(60);
        assertThat(test.getPassingScore()).isEqualTo(70);
        assertThat(test.getIsActive()).isTrue();
        assertThat(test.getCreatedBy()).isEqualTo(creator);
        assertThat(test.getTotalQuestions()).isZero();
    }

    @Test
    void shouldAddQuestionToTest() {
        LocalDateTime beforeUpdate = test.getUpdatedAt();
        
        test.addQuestion(question);
        
        assertThat(test.getQuestions()).hasSize(1);
        assertThat(test.getQuestions()).contains(question);
        assertThat(test.getTotalQuestions()).isEqualTo(1);
        assertThat(question.getTest()).isEqualTo(test);
        assertThat(test.getUpdatedAt()).isAfter(beforeUpdate);
    }

    @Test
    void shouldRemoveQuestionFromTest() {
        test.addQuestion(question);
        LocalDateTime beforeUpdate = test.getUpdatedAt();
        
        test.removeQuestion(question);
        
        assertThat(test.getQuestions()).isEmpty();
        assertThat(test.getTotalQuestions()).isZero();
        assertThat(question.getTest()).isNull();
        assertThat(test.getUpdatedAt()).isAfter(beforeUpdate);
    }

    @Test
    void shouldActivateTest() {
        test.setIsActive(false);
        LocalDateTime beforeActivation = test.getUpdatedAt();
        
        test.activate();
        
        assertThat(test.getIsActive()).isTrue();
        assertThat(test.getUpdatedAt()).isAfter(beforeActivation);
    }

    @Test
    void shouldDeactivateTest() {
        LocalDateTime beforeDeactivation = test.getUpdatedAt();
        
        test.deactivate();
        
        assertThat(test.getIsActive()).isFalse();
        assertThat(test.getUpdatedAt()).isAfter(beforeDeactivation);
    }

    @Test
    void shouldReturnTrueForCanBeStartedByWhenActiveAndHasQuestions() {
        test.addQuestion(question);
        
        boolean canBeStarted = test.canBeStartedBy(creator);
        
        assertThat(canBeStarted).isTrue();
    }

    @Test
    void shouldReturnFalseForCanBeStartedByWhenInactive() {
        test.setIsActive(false);
        test.addQuestion(question);
        
        boolean canBeStarted = test.canBeStartedBy(creator);
        
        assertThat(canBeStarted).isFalse();
    }

    @Test
    void shouldReturnFalseForCanBeStartedByWhenNoQuestions() {
        boolean canBeStarted = test.canBeStartedBy(creator);
        
        assertThat(canBeStarted).isFalse();
    }

    @Test
    void shouldCalculateTotalPoints() {
        Question question1 = Question.builder()
                .points(5)
                .build();
        Question question2 = Question.builder()
                .points(10)
                .build();
        
        test.addQuestion(question1);
        test.addQuestion(question2);
        
        int totalPoints = test.getTotalPoints();
        
        assertThat(totalPoints).isEqualTo(15);
    }
}
