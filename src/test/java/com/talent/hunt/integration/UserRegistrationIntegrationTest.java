package com.talent.hunt.integration;

import com.talent.hunt.application.dto.UserRegistrationDto;
import com.talent.hunt.application.service.UserService;
import com.talent.hunt.domain.model.User;
import com.talent.hunt.domain.model.UserRole;
import com.talent.hunt.domain.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@SpringBootTest(classes = com.talent.hunt.TalentHuntApplication.class)
@ActiveProfiles("test")
@Transactional
class UserRegistrationIntegrationTest {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private UserRegistrationDto registrationDto;

    @BeforeEach
    void setUp() {
        registrationDto = UserRegistrationDto.builder()
                .username("integrationtest")
                .email("<EMAIL>")
                .password("password123")
                .confirmPassword("password123")
                .firstName("Integration")
                .lastName("Test")
                .build();
    }

    @Test
    void shouldRegisterUserEndToEnd() {
        // When
        User registeredUser = userService.registerUser(registrationDto);

        // Then
        assertThat(registeredUser).isNotNull();
        assertThat(registeredUser.getId()).isNotNull();
        assertThat(registeredUser.getUsername()).isEqualTo("integrationtest");
        assertThat(registeredUser.getEmail()).isEqualTo("<EMAIL>");
        assertThat(registeredUser.getFirstName()).isEqualTo("Integration");
        assertThat(registeredUser.getLastName()).isEqualTo("Test");
        assertThat(registeredUser.getRole()).isEqualTo(UserRole.USER);
        assertThat(registeredUser.getIsActive()).isTrue();
        assertThat(registeredUser.getCreatedAt()).isNotNull();
        assertThat(registeredUser.getUpdatedAt()).isNotNull();

        // Verify password is encoded
        assertThat(passwordEncoder.matches("password123", registeredUser.getPasswordHash())).isTrue();

        // Verify user is persisted in database
        User foundUser = userRepository.findByUsername("integrationtest").orElse(null);
        assertThat(foundUser).isNotNull();
        assertThat(foundUser.getId()).isEqualTo(registeredUser.getId());
    }

    @Test
    void shouldPreventDuplicateUsernameRegistration() {
        // Given - register first user
        userService.registerUser(registrationDto);

        // When/Then - try to register with same username
        UserRegistrationDto duplicateDto = UserRegistrationDto.builder()
                .username("integrationtest")
                .email("<EMAIL>")
                .password("password123")
                .confirmPassword("password123")
                .firstName("Different")
                .lastName("User")
                .build();

        assertThatThrownBy(() -> userService.registerUser(duplicateDto))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Username already exists: integrationtest");
    }

    @Test
    void shouldPreventDuplicateEmailRegistration() {
        // Given - register first user
        userService.registerUser(registrationDto);

        // When/Then - try to register with same email
        UserRegistrationDto duplicateDto = UserRegistrationDto.builder()
                .username("differentuser")
                .email("<EMAIL>")
                .password("password123")
                .confirmPassword("password123")
                .firstName("Different")
                .lastName("User")
                .build();

        assertThatThrownBy(() -> userService.registerUser(duplicateDto))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Email already exists: <EMAIL>");
    }

    @Test
    void shouldLoadUserByUsernameAfterRegistration() {
        // Given
        User registeredUser = userService.registerUser(registrationDto);

        // When
        User loadedUser = (User) userService.loadUserByUsername("integrationtest");

        // Then
        assertThat(loadedUser).isNotNull();
        assertThat(loadedUser.getId()).isEqualTo(registeredUser.getId());
        assertThat(loadedUser.getUsername()).isEqualTo("integrationtest");
        assertThat(loadedUser.getAuthorities()).hasSize(1);
        assertThat(loadedUser.getAuthorities().iterator().next().getAuthority()).isEqualTo("ROLE_USER");
    }
}
