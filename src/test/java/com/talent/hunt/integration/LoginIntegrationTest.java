package com.talent.hunt.integration;

import com.talent.hunt.application.service.UserService;
import com.talent.hunt.domain.model.User;
import com.talent.hunt.domain.model.UserRole;
import com.talent.hunt.domain.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestBuilders.formLogin;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.security.test.web.servlet.response.SecurityMockMvcResultMatchers.authenticated;
import static org.springframework.security.test.web.servlet.response.SecurityMockMvcResultMatchers.unauthenticated;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class LoginIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserService userService;

    private User testUser;
    private User adminUser;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = User.builder()
                .username("testuser")
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("password123"))
                .firstName("Test")
                .lastName("User")
                .role(UserRole.USER)
                .isActive(true)
                .build();
        testUser = userRepository.save(testUser);

        // Create admin user
        adminUser = User.builder()
                .username("testadmin")
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("admin123"))
                .firstName("Admin")
                .lastName("User")
                .role(UserRole.ADMIN)
                .isActive(true)
                .build();
        adminUser = userRepository.save(adminUser);
    }

    @Test
    void shouldDisplayLoginPage() throws Exception {
        mockMvc.perform(get("/login"))
                .andExpect(status().isOk())
                .andExpect(view().name("auth/login"))
                .andExpect(model().attributeExists("loginDto"));
    }

    @Test
    void shouldRedirectToLoginWhenAccessingProtectedResource() throws Exception {
        mockMvc.perform(get("/dashboard"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrlPattern("**/login"));
    }

    @Test
    void shouldAuthenticateValidUserCredentials() throws Exception {
        mockMvc.perform(formLogin("/login")
                        .user("testuser")
                        .password("password123"))
                .andExpect(authenticated().withUsername("testuser"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/dashboard"));
    }

    @Test
    void shouldAuthenticateValidAdminCredentials() throws Exception {
        mockMvc.perform(formLogin("/login")
                        .user("testadmin")
                        .password("admin123"))
                .andExpect(authenticated().withUsername("testadmin"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/admin/dashboard"));
    }

    @Test
    void shouldRejectInvalidCredentials() throws Exception {
        mockMvc.perform(formLogin("/login")
                        .user("testuser")
                        .password("wrongpassword"))
                .andExpect(unauthenticated())
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=true"));
    }

    @Test
    void shouldRejectNonExistentUser() throws Exception {
        mockMvc.perform(formLogin("/login")
                        .user("nonexistent")
                        .password("password123"))
                .andExpect(unauthenticated())
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=true"));
    }

    @Test
    void shouldRejectInactiveUser() throws Exception {
        // Deactivate user
        testUser.deactivate();
        userRepository.save(testUser);

        mockMvc.perform(formLogin("/login")
                        .user("testuser")
                        .password("password123"))
                .andExpect(unauthenticated())
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=true"));
    }

    @Test
    void shouldDisplayErrorMessageOnFailedLogin() throws Exception {
        mockMvc.perform(get("/login?error=true"))
                .andExpect(status().isOk())
                .andExpect(view().name("auth/login"))
                .andExpect(model().attribute("errorMessage", "Invalid username or password"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void shouldAccessDashboardWhenAuthenticated() throws Exception {
        mockMvc.perform(get("/dashboard"))
                .andExpect(status().isOk())
                .andExpect(view().name("dashboard/index"));
    }

    @Test
    void shouldLogoutSuccessfully() throws Exception {
        // First login
        mockMvc.perform(formLogin("/login")
                        .user("testuser")
                        .password("password123"))
                .andExpect(authenticated());

        // Then logout
        mockMvc.perform(post("/logout").with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?logout=true"))
                .andExpect(unauthenticated());
    }

    @Test
    void shouldDisplayLogoutMessageAfterLogout() throws Exception {
        mockMvc.perform(get("/login?logout=true"))
                .andExpect(status().isOk())
                .andExpect(view().name("auth/login"))
                .andExpect(model().attribute("successMessage", "You have been logged out successfully"));
    }

    @Test
    void shouldLoadUserDetailsCorrectly() throws Exception {
        User loadedUser = (User) userService.loadUserByUsername("testuser");
        
        assert loadedUser != null;
        assert loadedUser.getUsername().equals("testuser");
        assert loadedUser.getAuthorities().size() == 1;
        assert loadedUser.getAuthorities().iterator().next().getAuthority().equals("ROLE_USER");
        assert loadedUser.isEnabled();
        assert loadedUser.isAccountNonLocked();
        assert passwordEncoder.matches("password123", loadedUser.getPassword());
    }
}
