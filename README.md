# Talent Hunt - Online Test Platform

A comprehensive online test platform built with Java Spring Boot, following Domain Driven Design (DDD) principles and SOLID design patterns. The platform enables users to create, manage, and take multiple-choice question (MCQ) tests with a complete authentication and authorization system.

## 🏗️ Architecture Overview

### System Architecture

The application follows a layered architecture based on Domain Driven Design:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Controllers   │  │   Thymeleaf     │  │    DTOs     │ │
│  │                 │  │   Templates     │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    Services     │  │      DTOs       │  │  Validation │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    Entities     │  │  Repositories   │  │   Services  │ │
│  │                 │  │  (Interfaces)   │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │  JPA Repos      │  │    Security     │  │    Config   │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Database Layer                         │
│                    PostgreSQL Database                      │
└─────────────────────────────────────────────────────────────┘
```

### Core Domain Models

```
User ──────────┐
               │
               ▼
Test ──────► Question ──────► AnswerOption
 │              │
 │              │
 ▼              ▼
TestSession ──► UserResponse
```

## 🚀 Technology Stack

- **Backend Framework**: Spring Boot 3.5.7
- **Language**: Java 17
- **Database**: PostgreSQL
- **Migration**: Flyway
- **Security**: Spring Security 6
- **Template Engine**: Thymeleaf
- **Build Tool**: Maven
- **Testing**: JUnit 5, Mockito, AssertJ
- **ORM**: Spring Data JPA with Hibernate

## 📋 Features

### Core Features
- ✅ User Registration and Authentication
- ✅ Role-based Authorization (USER, ADMIN)
- ✅ Test Creation and Management
- ✅ Question and Answer Management
- ✅ Test Session Management
- ✅ Real-time Test Taking
- ✅ Automatic Scoring
- ✅ Test Results and Analytics

### Security Features
- ✅ BCrypt Password Encryption
- ✅ Session Management
- ✅ CSRF Protection
- ✅ Role-based Access Control

### Technical Features
- ✅ Domain Driven Design (DDD)
- ✅ SOLID Principles
- ✅ Comprehensive Unit Testing
- ✅ Database Migrations with Flyway
- ✅ Responsive Web Interface

## 🗄️ Database Schema

### Core Tables

```sql
users
├── id (BIGSERIAL PRIMARY KEY)
├── username (VARCHAR UNIQUE)
├── email (VARCHAR UNIQUE)
├── password_hash (VARCHAR)
├── first_name (VARCHAR)
├── last_name (VARCHAR)
├── role (VARCHAR)
├── is_active (BOOLEAN)
├── created_at (TIMESTAMP)
└── updated_at (TIMESTAMP)

tests
├── id (BIGSERIAL PRIMARY KEY)
├── title (VARCHAR)
├── description (TEXT)
├── duration_minutes (INTEGER)
├── total_questions (INTEGER)
├── passing_score (INTEGER)
├── is_active (BOOLEAN)
├── created_by (BIGINT FK → users.id)
├── created_at (TIMESTAMP)
└── updated_at (TIMESTAMP)

questions
├── id (BIGSERIAL PRIMARY KEY)
├── test_id (BIGINT FK → tests.id)
├── question_text (TEXT)
├── question_type (VARCHAR)
├── points (INTEGER)
├── order_index (INTEGER)
├── created_at (TIMESTAMP)
└── updated_at (TIMESTAMP)

answer_options
├── id (BIGSERIAL PRIMARY KEY)
├── question_id (BIGINT FK → questions.id)
├── option_text (TEXT)
├── is_correct (BOOLEAN)
├── order_index (INTEGER)
└── created_at (TIMESTAMP)

test_sessions
├── id (BIGSERIAL PRIMARY KEY)
├── test_id (BIGINT FK → tests.id)
├── user_id (BIGINT FK → users.id)
├── session_status (VARCHAR)
├── start_time (TIMESTAMP)
├── end_time (TIMESTAMP)
├── score (INTEGER)
├── total_points (INTEGER)
├── created_at (TIMESTAMP)
└── updated_at (TIMESTAMP)

user_responses
├── id (BIGSERIAL PRIMARY KEY)
├── test_session_id (BIGINT FK → test_sessions.id)
├── question_id (BIGINT FK → questions.id)
├── selected_option_id (BIGINT FK → answer_options.id)
├── is_correct (BOOLEAN)
├── points_earned (INTEGER)
└── answered_at (TIMESTAMP)
```

## 🔄 Application Flow

### User Registration & Authentication Flow
```
1. User visits /register
2. Fills registration form
3. System validates data
4. Password encrypted with BCrypt
5. User saved to database
6. Redirect to login page
7. User logs in with credentials
8. Spring Security authenticates
9. Session created
10. Redirect to dashboard
```

### Test Creation Flow
```
1. Authenticated user visits /tests/create
2. Fills test details form
3. System creates test entity
4. Redirect to question management
5. User adds questions with options
6. System validates question data
7. Questions saved with correct answers
8. Test becomes available for taking
```

### Test Taking Flow
```
1. User selects test from dashboard
2. System creates test session
3. Session status: IN_PROGRESS
4. User answers questions
5. System saves responses
6. User completes test
7. System calculates score
8. Session status: COMPLETED
9. Results displayed
```

## 🛠️ Setup Instructions

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+
- Git

### Database Setup
1. Install PostgreSQL
2. Create database and user:
```sql
CREATE DATABASE talent_hunt_db;
CREATE USER talent_hunt_user WITH PASSWORD 'talent_hunt_password';
GRANT ALL PRIVILEGES ON DATABASE talent_hunt_db TO talent_hunt_user;
```

### Application Setup
1. Clone the repository:
```bash
git clone <repository-url>
cd talent-hunt
```

2. Configure database connection in `src/main/resources/application.properties`:
```properties
spring.datasource.url=***********************************************
spring.datasource.username=talent_hunt_user
spring.datasource.password=talent_hunt_password
```

3. Run the application:
```bash
./mvnw spring-boot:run
```

4. Access the application:
- URL: http://localhost:8080
- Default admin credentials: admin / admin123

### Running Tests
```bash
# Run all tests
./mvnw test

# Run specific test classes
./mvnw test -Dtest="UserServiceTest,TestServiceTest"

# Run with coverage
./mvnw test jacoco:report
```

## 📁 Project Structure

```
src/
├── main/
│   ├── java/com/talent/hunt/
│   │   ├── TalentHuntApplication.java
│   │   ├── application/
│   │   │   ├── dto/           # Data Transfer Objects
│   │   │   └── service/       # Application Services
│   │   ├── domain/
│   │   │   ├── model/         # Domain Entities
│   │   │   └── repository/    # Repository Interfaces
│   │   ├── infrastructure/
│   │   │   ├── config/        # Configuration Classes
│   │   │   └── security/      # Security Configuration
│   │   └── presentation/
│   │       ├── controller/    # Web Controllers
│   │       └── dto/           # Presentation DTOs
│   └── resources/
│       ├── db/migration/      # Flyway Migration Scripts
│       ├── templates/         # Thymeleaf Templates
│       └── application.properties
└── test/
    └── java/com/talent/hunt/
        ├── application/service/  # Service Tests
        ├── domain/model/        # Domain Tests
        └── integration/         # Integration Tests
```

## 🔐 Security Implementation

### Authentication
- **Form-based authentication** with custom login page
- **BCrypt password encoding** for secure password storage
- **Session management** with configurable timeout
- **Remember-me functionality** for user convenience

### Authorization
- **Role-based access control** (RBAC)
- **Method-level security** with @PreAuthorize annotations
- **URL-based security** configuration
- **CSRF protection** enabled by default

### Security Configuration
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/", "/register", "/login", "/css/**", "/js/**").permitAll()
                .requestMatchers("/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .formLogin(form -> form
                .loginPage("/login")
                .defaultSuccessUrl("/dashboard", true)
                .failureUrl("/login?error=true")
            )
            .logout(logout -> logout
                .logoutUrl("/logout")
                .logoutSuccessUrl("/login?logout=true")
                .invalidateHttpSession(true)
            )
            .build();
    }
}
```

## 🧪 Testing Strategy

### Unit Tests
- **Domain Model Tests**: Validate business logic and entity behavior
- **Service Layer Tests**: Test application services with mocked dependencies
- **Repository Tests**: Validate data access layer functionality

### Test Coverage
- **Domain Models**: 100% coverage of business logic
- **Application Services**: Comprehensive testing of all service methods
- **Integration Tests**: End-to-end testing of critical user flows

### Testing Tools
- **JUnit 5**: Primary testing framework
- **Mockito**: Mocking framework for unit tests
- **AssertJ**: Fluent assertion library
- **H2 Database**: In-memory database for testing
- **Spring Boot Test**: Integration testing support

## 🚀 Deployment

### Production Configuration
1. Update `application-prod.properties`:
```properties
# Database
spring.datasource.url=${DATABASE_URL}
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}

# JPA
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

# Security
server.servlet.session.timeout=30m
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.http-only=true

# Logging
logging.level.com.talent.hunt=INFO
logging.level.org.springframework.security=WARN
```

2. Build the application:
```bash
./mvnw clean package -Pprod
```

3. Run with production profile:
```bash
java -jar target/talent-hunt-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

### Docker Deployment
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/talent-hunt-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 📊 API Endpoints

### Authentication Endpoints
- `GET /login` - Login page
- `POST /login` - Process login
- `GET /register` - Registration page
- `POST /register` - Process registration
- `POST /logout` - Logout user

### Dashboard Endpoints
- `GET /dashboard` - User dashboard
- `GET /profile` - User profile page
- `POST /profile` - Update user profile

### Test Management Endpoints
- `GET /tests` - List all tests
- `GET /tests/create` - Create test form
- `POST /tests` - Create new test
- `GET /tests/{id}` - View test details
- `GET /tests/{id}/edit` - Edit test form
- `PUT /tests/{id}` - Update test
- `DELETE /tests/{id}` - Delete test

### Question Management Endpoints
- `GET /tests/{testId}/questions` - List test questions
- `GET /tests/{testId}/questions/create` - Create question form
- `POST /tests/{testId}/questions` - Create new question
- `GET /questions/{id}/edit` - Edit question form
- `PUT /questions/{id}` - Update question
- `DELETE /questions/{id}` - Delete question

### Test Taking Endpoints
- `GET /tests/{id}/take` - Start test session
- `POST /tests/{id}/take` - Submit test answers
- `GET /sessions/{id}/results` - View test results

## 🔧 Configuration

### Application Properties
```properties
# Server Configuration
server.port=8080
server.servlet.context-path=/

# Database Configuration
spring.datasource.url=***********************************************
spring.datasource.username=talent_hunt_user
spring.datasource.password=talent_hunt_password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Security Configuration
spring.security.user.name=admin
spring.security.user.password=admin123
spring.security.user.roles=ADMIN

# Session Configuration
server.servlet.session.timeout=30m
server.servlet.session.cookie.max-age=1800
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify PostgreSQL is running
   - Check database credentials
   - Ensure database exists

2. **Flyway Migration Failed**
   - Check migration scripts syntax
   - Verify database permissions
   - Clear flyway_schema_history if needed

3. **Authentication Issues**
   - Clear browser cookies
   - Check user credentials
   - Verify user is active

4. **Test Failures**
   - Ensure H2 dependency is in test scope
   - Check test database configuration
   - Verify test data setup

### Logging Configuration
```properties
# Enable debug logging
logging.level.com.talent.hunt=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow SOLID principles
- Write comprehensive tests
- Use meaningful commit messages
- Update documentation
- Follow Java coding conventions

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Authors

- **Development Team** - Initial work and ongoing maintenance

## 🙏 Acknowledgments

- Spring Boot team for the excellent framework
- Thymeleaf team for the template engine
- PostgreSQL community for the robust database
- All contributors who helped improve this project
