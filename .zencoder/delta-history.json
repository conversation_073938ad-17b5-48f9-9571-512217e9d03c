{"snapshots": {"/home/<USER>/Downloads/telant-hunt/pom.xml": {"filePath": "/home/<USER>/Downloads/telant-hunt/pom.xml", "baseContent": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<project xmlns=\"http://maven.apache.org/POM/4.0.0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n\txsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd\">\n\t<modelVersion>4.0.0</modelVersion>\n\t<parent>\n\t\t<groupId>org.springframework.boot</groupId>\n\t\t<artifactId>spring-boot-starter-parent</artifactId>\n\t\t<version>3.5.7</version>\n\t\t<relativePath/> <!-- lookup parent from repository -->\n\t</parent>\n\t<groupId>com.talent.hunt</groupId>\n\t<artifactId>telant-hunt</artifactId>\n\t<version>0.0.1-SNAPSHOT</version>\n\t<name>telant-hunt</name>\n\t<description>Demo project for Spring Boot</description>\n\t<url/>\n\t<licenses>\n\t\t<license/>\n\t</licenses>\n\t<developers>\n\t\t<developer/>\n\t</developers>\n\t<scm>\n\t\t<connection/>\n\t\t<developerConnection/>\n\t\t<tag/>\n\t\t<url/>\n\t</scm>\n\t<properties>\n\t\t<java.version>17</java.version>\n\t</properties>\n\t<dependencies>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-data-jpa</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-thymeleaf</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-web</artifactId>\n\t\t</dependency>\n\n\t\t<dependency>\n\t\t\t<groupId>org.postgresql</groupId>\n\t\t\t<artifactId>postgresql</artifactId>\n\t\t\t<scope>runtime</scope>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.projectlombok</groupId>\n\t\t\t<artifactId>lombok</artifactId>\n\t\t\t<optional>true</optional>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-security</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-validation</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.flywaydb</groupId>\n\t\t\t<artifactId>flyway-core</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.flywaydb</groupId>\n\t\t\t<artifactId>flyway-database-postgresql</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-test</artifactId>\n\t\t\t<scope>test</scope>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.assertj</groupId>\n\t\t\t<artifactId>assertj-core</artifactId>\n\t\t\t<scope>test</scope>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.security</groupId>\n\t\t\t<artifactId>spring-security-test</artifactId>\n\t\t\t<scope>test</scope>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>com.h2database</groupId>\n\t\t\t<artifactId>h2</artifactId>\n\t\t\t<scope>test</scope>\n\t\t</dependency>\n\t</dependencies>\n\n\t<build>\n\t\t<plugins>\n\t\t\t<plugin>\n\t\t\t\t<groupId>org.apache.maven.plugins</groupId>\n\t\t\t\t<artifactId>maven-compiler-plugin</artifactId>\n\t\t\t\t<configuration>\n\t\t\t\t\t<annotationProcessorPaths>\n\t\t\t\t\t\t<path>\n\t\t\t\t\t\t\t<groupId>org.projectlombok</groupId>\n\t\t\t\t\t\t\t<artifactId>lombok</artifactId>\n\t\t\t\t\t\t</path>\n\t\t\t\t\t</annotationProcessorPaths>\n\t\t\t\t</configuration>\n\t\t\t</plugin>\n\t\t\t<plugin>\n\t\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t\t<artifactId>spring-boot-maven-plugin</artifactId>\n\t\t\t\t<configuration>\n\t\t\t\t\t<excludes>\n\t\t\t\t\t\t<exclude>\n\t\t\t\t\t\t\t<groupId>org.projectlombok</groupId>\n\t\t\t\t\t\t\t<artifactId>lombok</artifactId>\n\t\t\t\t\t\t</exclude>\n\t\t\t\t\t</excludes>\n\t\t\t\t</configuration>\n\t\t\t</plugin>\n\t\t</plugins>\n\t</build>\n\n</project>\n", "baseTimestamp": 1761797900418}, "/home/<USER>/Downloads/telant-hunt/src/main/resources/application.properties": {"filePath": "/home/<USER>/Downloads/telant-hunt/src/main/resources/application.properties", "baseContent": "# Application Configuration\nspring.application.name=talent-hunt\n\n# Database Configuration\nspring.datasource.url=****************************************************************************************************************************************************************************************# JPA Configuration\nspring.jpa.hibernate.ddl-auto=validate\nspring.jpa.show-sql=false\nspring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect\nspring.jpa.properties.hibernate.format_sql=true\n\n# Flyway Configuration\nspring.flyway.enabled=true\nspring.flyway.locations=classpath:db/migration\nspring.flyway.baseline-on-migrate=true\n\n# Thymeleaf Configuration\nspring.thymeleaf.cache=false\nspring.thymeleaf.prefix=classpath:/templates/\nspring.thymeleaf.suffix=.html\n\n# Logging Configuration\nlogging.level.com.talent.hunt=DEBUG\nlogging.level.org.springframework.security=DEBUG\n", "baseTimestamp": 1762272782749}, "/home/<USER>/Downloads/telant-hunt/src/main/java/com/talent/hunt/infrastructure/config/SecurityConfig.java": {"filePath": "/home/<USER>/Downloads/telant-hunt/src/main/java/com/talent/hunt/infrastructure/config/SecurityConfig.java", "baseContent": "package com.talent.hunt.infrastructure.config;\n\nimport com.talent.hunt.application.service.UserService;\nimport lombok.RequiredArgsConstructor;\nimport org.springframework.context.annotation.Bean;\nimport org.springframework.context.annotation.Configuration;\nimport org.springframework.security.authentication.AuthenticationManager;\nimport org.springframework.security.authentication.dao.DaoAuthenticationProvider;\nimport org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;\nimport org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;\nimport org.springframework.security.config.annotation.web.builders.HttpSecurity;\nimport org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;\nimport org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;\nimport org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;\nimport org.springframework.security.crypto.password.PasswordEncoder;\nimport org.springframework.security.web.SecurityFilterChain;\nimport org.springframework.security.web.authentication.AuthenticationSuccessHandler;\nimport org.springframework.security.web.util.matcher.AntPathRequestMatcher;\n\n@Configuration\n@EnableWebSecurity\n@EnableMethodSecurity\n@RequiredArgsConstructor\npublic class SecurityConfig {\n\n    \n    private final UserService userService;\n\n    @Bean\n    public PasswordEncoder passwordEncoder() {\n        return new BCryptPasswordEncoder();\n    }\n\n    @Bean\n    public DaoAuthenticationProvider authenticationProvider() {\n        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();\n        authProvider.setUserDetailsService(userService);\n        authProvider.setPasswordEncoder(passwordEncoder());\n        return authProvider;\n    }\n\n    @Bean\n    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {\n        return config.getAuthenticationManager();\n    }\n\n    @Bean\n    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {\n        http\n            .csrf(AbstractHttpConfigurer::disable)\n            .authorizeHttpRequests(authz -> authz\n                .requestMatchers(\"/\", \"/home\", \"/register\", \"/login\", \"/css/**\", \"/js/**\", \"/images/**\").permitAll()\n                .requestMatchers(\"/admin/**\").hasRole(\"ADMIN\")\n                .anyRequest().authenticated()\n            )\n            .formLogin(form -> form\n                .loginPage(\"/login\")\n                .loginProcessingUrl(\"/login\")\n                .successHandler(authenticationSuccessHandler())\n                .failureUrl(\"/login?error=true\")\n                .permitAll()\n            )\n            .logout(logout -> logout\n                .logoutRequestMatcher(new AntPathRequestMatcher(\"/logout\"))\n                .logoutSuccessUrl(\"/login?logout=true\")\n                .deleteCookies(\"JSESSIONID\")\n                .invalidateHttpSession(true)\n                .permitAll()\n            )\n            .sessionManagement(session -> session\n                .maximumSessions(1)\n                .maxSessionsPreventsLogin(false)\n            );\n\n        return http.build();\n    }\n\n    @Bean\n    public AuthenticationSuccessHandler authenticationSuccessHandler() {\n        return (request, response, authentication) -> {\n            String redirectUrl = \"/dashboard\";\n            \n            // Check if user has admin role\n            boolean isAdmin = authentication.getAuthorities().stream()\n                    .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(\"ROLE_ADMIN\"));\n            \n            if (isAdmin) {\n                redirectUrl = \"/admin/dashboard\";\n            }\n            \n            response.sendRedirect(redirectUrl);\n        };\n    }\n}\n", "baseTimestamp": 1762272830829, "deltas": [{"timestamp": 1762272835537, "changes": [{"type": "INSERT", "lineNumber": 4, "content": "import org.springframework.beans.factory.annotation.Autowired;"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    "}, {"type": "INSERT", "lineNumber": 26, "content": "    @Autowired"}]}, {"timestamp": 1762272852117, "changes": [{"type": "DELETE", "lineNumber": 25, "oldContent": ""}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    @Autowired"}, {"type": "INSERT", "lineNumber": 25, "content": "    "}]}, {"timestamp": 1762272859533, "changes": [{"type": "INSERT", "lineNumber": 28, "content": "    "}]}, {"timestamp": 1762272861832, "changes": [{"type": "MODIFY", "lineNumber": 25, "content": "", "oldContent": "    "}, {"type": "MODIFY", "lineNumber": 28, "content": "    public SecurityConfig(UserService userService) {", "oldContent": "    "}, {"type": "INSERT", "lineNumber": 29, "content": "        this.userService = userService;"}, {"type": "INSERT", "lineNumber": 30, "content": "    }"}, {"type": "INSERT", "lineNumber": 31, "content": ""}]}, {"timestamp": 1762272865691, "changes": [{"type": "DELETE", "lineNumber": 23, "oldContent": "@RequiredArgsConstructor"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "    @Bean"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "    public PasswordEncoder passwordEncoder() {"}, {"type": "INSERT", "lineNumber": 31, "content": "    @Bean"}, {"type": "INSERT", "lineNumber": 32, "content": "    public PasswordEncoder passwordEncoder() {"}]}]}, "/home/<USER>/Downloads/telant-hunt/src/main/resources/templates/auth/login.html": {"filePath": "/home/<USER>/Downloads/telant-hunt/src/main/resources/templates/auth/login.html", "baseContent": "<!DOCTYPE html>\n<html lang=\"en\" xmlns:th=\"http://www.thymeleaf.org\">\n<head>\n    <title>Login</title>\n</head>\n<body>\n    <main>\n        <div class=\"container\">\n            <div class=\"row justify-content-center\">\n                <div class=\"col-md-6 col-lg-4\">\n                    <div class=\"card shadow mt-5\">\n                        <div class=\"card-header bg-primary text-white text-center\">\n                            <h4 class=\"mb-0\">\n                                <i class=\"fas fa-sign-in-alt me-2\"></i>Login\n                            </h4>\n                        </div>\n                        <div class=\"card-body\">\n                            <form th:action=\"@{/login}\" method=\"post\">\n                                <div class=\"mb-3\">\n                                    <label for=\"username\" class=\"form-label\">Username</label>\n                                    <div class=\"input-group\">\n                                        <span class=\"input-group-text\">\n                                            <i class=\"fas fa-user\"></i>\n                                        </span>\n                                        <input type=\"text\" \n                                               class=\"form-control\" \n                                               id=\"username\" \n                                               name=\"username\" \n                                               required \n                                               placeholder=\"Enter your username\">\n                                    </div>\n                                </div>\n                                \n                                <div class=\"mb-3\">\n                                    <label for=\"password\" class=\"form-label\">Password</label>\n                                    <div class=\"input-group\">\n                                        <span class=\"input-group-text\">\n                                            <i class=\"fas fa-lock\"></i>\n                                        </span>\n                                        <input type=\"password\" \n                                               class=\"form-control\" \n                                               id=\"password\" \n                                               name=\"password\" \n                                               required \n                                               placeholder=\"Enter your password\">\n                                    </div>\n                                </div>\n                                \n                                <div class=\"d-grid\">\n                                    <button type=\"submit\" class=\"btn btn-primary\">\n                                        <i class=\"fas fa-sign-in-alt me-2\"></i>Login\n                                    </button>\n                                </div>\n                            </form>\n                        </div>\n                        <div class=\"card-footer text-center\">\n                            <p class=\"mb-0\">\n                                Don't have an account? \n                                <a href=\"/register\" class=\"text-decoration-none\">Register here</a>\n                            </p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </main>\n</body>\n</html>\n", "baseTimestamp": *************}}}